import { DashboardContainer } from "@/components/dashboard/DashboardContainer";
import { LoadingScreen } from "@/components/ui/loading-screen";
import { useAuth } from "@/hooks/useAuth";
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";

export function DashboardPage() {
  const { user, organization, isLoading } = useAuth();
  const navigate = useNavigate();

  // Redirect to setup if no organization after loading
  useEffect(() => {
    if (!isLoading && user && !organization) {
      navigate("/setup");
    }
  }, [isLoading, user, organization, navigate]);

  // Show loading screen while auth is loading
  if (isLoading) {
    return <LoadingScreen message="Loading your dashboard..." />;
  }

  // If no user, let the auth system handle the redirect
  if (!user) {
    return <LoadingScreen message="Authenticating..." />;
  }

  // If no organization and not loading, redirect to setup
  if (!organization) {
    return <LoadingScreen message="Redirecting to setup..." />;
  }

  // Render the dashboard container component
  return <DashboardContainer />;
}
