import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { useAuth } from "@/hooks/useAuth";
import { supabase } from "@/lib/supabase";
import { FullSetupPage } from "@/pages";
import { useEffect, useState } from "react";
import { Navigate, Route, Routes, useNavigate } from "react-router-dom";
import { CreateOrganizationPage } from "./CreateOrganizationPage";
import { InvitedUserSetup } from "./InvitedUserSetup";

export function SetupRouter() {
  const { user, organization } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [hasPendingInvites, setHasPendingInvites] = useState(false);
  const [hasExistingOrgs, setHasExistingOrgs] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const checkUserStatus = async () => {
      if (!user) {
        setIsLoading(false);
        return;
      }

      try {
        // First check secure storage for existing organizations
        const keys = Object.keys(sessionStorage);
        const orgKeys = keys.filter(
          (key) => key.startsWith("spritely_secure_org_") && key.includes(user.id),
        );

        if (orgKeys.length > 0) {
          // User has organization data in secure storage
          setHasExistingOrgs(true);
          setIsLoading(false);
          return;
        }

        // Check for pending invites
        const { data: invites, error: invitesError } = await supabase
          .from("organization_invites")
          .select("id")
          .eq("email", user.email!)
          .eq("status", "pending")
          .limit(1);

        if (invitesError) {
          // Don't throw, just log and continue
          console.error("Error checking invites:", invitesError);
        } else {
          setHasPendingInvites(invites && invites.length > 0);
        }

        // Check for existing organizations
        const { data: roles, error: rolesError } = await supabase
          .from("user_roles")
          .select("organization_id")
          .eq("user_id", user.id)
          .not("organization_id", "is", null)
          .limit(1);

        if (rolesError) {
          // Don't throw, just log and continue
          console.error("Error checking roles:", rolesError);
        } else {
          setHasExistingOrgs(roles && roles.length > 0);
        }
      } catch (error) {
        // Just log the error, don't prevent the component from rendering
        console.error("Error checking user status:", error);
      } finally {
        setIsLoading(false);
      }
    };

    checkUserStatus();
  }, [user]);

  useEffect(() => {
    // If user already has an organization selected, redirect to dashboard
    if (organization) {
      // Check if this is a system admin with no real organization
      if (organization.id === "system-admin-no-org") {
        // Redirect to organizations page to select or create an organization
        navigate("/organizations", { replace: true });
      } else {
        // Redirect to dashboard for normal users with organizations
        navigate("/dashboard", { replace: true });
      }
    }
  }, [organization, navigate]);

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <LoadingSpinner />
      </div>
    );
  }

  if (!user) {
    return null; // ProtectedRoute will handle the redirect
  }

  // If user has existing orgs but no organization is selected,
  // they should go to the organization selection page
  if (hasExistingOrgs && !organization) {
    // Redirect silently
    return <Navigate to="/organizations" replace />;
  }

  return (
    <Routes>
      {/* Handle pending invites */}
      {hasPendingInvites ? (
        <Route path="/*" element={<InvitedUserSetup />} />
      ) : (
        <>
          {/* Organization creation flow */}
          <Route path="/organization" element={<CreateOrganizationPage />} />

          {/* Default setup flow for new users */}
          <Route path="/*" element={<FullSetupPage />} />
        </>
      )}
    </Routes>
  );
}




