import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { useAuth } from "@/hooks/useAuth";
import { supabase } from "@/lib/supabase";
import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

interface OrganizationInvite {
  id: string;
  organization: {
    id: string;
    name: string;
    type: string;
  };
  role: string;
  expires_at: string;
}

export function InvitedUserSetup() {
  const { user, setOrganization } = useAuth();
  const navigate = useNavigate();
  const [invites, setInvites] = useState<OrganizationInvite[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAccepting, setIsAccepting] = useState(false);

  useEffect(() => {
    const fetchInvites = async () => {
      if (!user?.email) return;

      try {
        const { data, error } = await supabase
          .from("organization_invites")
          .select(
            `
            id,
            role,
            expires_at,
            organization:organizations (
              id,
              name,
              type
            )
          `,
          )
          .eq("email", user.email)
          .eq("status", "pending")
          .order("created_at", { ascending: false });

        if (error) throw error;

        // Cast the data to unknown first to handle the structure mismatch
        const rawData = data as unknown as Array<{
          id: string;
          role: string;
          expires_at: string;
          organization: {
            id: string;
            name: string;
            type: string;
          };
        }>;

        // Transform the data to match our expected structure
        const transformedData: OrganizationInvite[] = rawData.map((item) => ({
          id: item.id,
          role: item.role,
          expires_at: item.expires_at,
          organization: {
            id: item.organization.id,
            name: item.organization.name,
            type: item.organization.type,
          },
        }));

        setInvites(transformedData);
      } catch (error) {
        console.error("Error fetching invites:", error);
        toast.error("Failed to load organization invites");
      } finally {
        setIsLoading(false);
      }
    };

    fetchInvites();
  }, [user?.email]);

  const handleAcceptInvite = async (invite: OrganizationInvite) => {
    if (!user) return;

    try {
      setIsAccepting(true);

      // Start a transaction
      const { error: transactionError } = await supabase.rpc(
        "accept_organization_invite",
        {
          invite_id: invite.id,
        },
      );

      if (transactionError) throw transactionError;

      // Update the organization context
      // Convert our simplified Organization to the full type expected by setOrganization
      const fullOrg = {
        id: invite.organization.id,
        name: invite.organization.name,
        type: invite.organization.type,
        // Add missing properties with default values
        settings: {},
        billing_info: {},
        created_at: new Date().toISOString(),
        subscription_tier: "free",
        updated_at: new Date().toISOString(),
        owner_id: user.id, // Add the required owner_id field
      };

      setOrganization(fullOrg);

      // Show success message
      toast.success("Successfully joined organization");

      // Navigate to dashboard
      navigate("/dashboard");
    } catch (error) {
      console.error("Error accepting invite:", error);
      toast.error("Failed to accept organization invite");
    } finally {
      setIsAccepting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <h2 className="text-lg font-semibold">Loading invites...</h2>
          <p className="text-sm text-muted-foreground">Please wait</p>
        </div>
      </div>
    );
  }

  if (invites.length === 0) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <h2 className="text-lg font-semibold">No Pending Invites</h2>
          <p className="text-sm text-muted-foreground">
            You don't have any pending organization invites
          </p>
          <Button className="mt-4" onClick={() => navigate("/dashboard")}>
            Go to Dashboard
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-2xl py-8">
      <h1 className="mb-6 text-2xl font-bold">Organization Invites</h1>
      <div className="space-y-4">
        {invites.map((invite) => (
          <Card key={invite.id}>
            <CardHeader>
              <CardTitle>{invite.organization.name}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="mb-4">
                <p className="text-sm text-muted-foreground">
                  You've been invited to join as a {invite.role}
                </p>
                <p className="text-xs text-muted-foreground">
                  Expires: {new Date(invite.expires_at).toLocaleDateString()}
                </p>
              </div>
              <Button
                onClick={() => handleAcceptInvite(invite)}
                disabled={isAccepting}
              >
                {isAccepting ? "Accepting..." : "Accept Invite"}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
