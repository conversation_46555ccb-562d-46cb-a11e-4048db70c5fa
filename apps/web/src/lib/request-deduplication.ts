/**
 * Request deduplication service to prevent duplicate API calls
 * This helps solve the massive performance issue where the same API calls
 * are made hundreds of times on a single page load
 */

interface PendingRequest<T> {
  promise: Promise<T>;
  timestamp: number;
}

class RequestDeduplicator {
  private pendingRequests = new Map<string, PendingRequest<any>>();
  private cache = new Map<string, { data: any; timestamp: number }>();
  private readonly CACHE_DURATION = 30 * 1000; // 30 seconds
  private readonly REQUEST_TIMEOUT = 10 * 1000; // 10 seconds

  /**
   * Deduplicate a request by key. If the same request is already pending,
   * return the existing promise. If cached data exists and is fresh, return it.
   */
  async deduplicate<T>(
    key: string,
    requestFn: () => Promise<T>,
    cacheDuration: number = this.CACHE_DURATION
  ): Promise<T> {
    // Check cache first
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < cacheDuration) {
      return cached.data;
    }

    // Check if request is already pending
    const pending = this.pendingRequests.get(key);
    if (pending && Date.now() - pending.timestamp < this.REQUEST_TIMEOUT) {
      return pending.promise;
    }

    // Create new request
    const promise = requestFn()
      .then((data) => {
        // Cache the result
        this.cache.set(key, { data, timestamp: Date.now() });
        // Remove from pending
        this.pendingRequests.delete(key);
        return data;
      })
      .catch((error) => {
        // Remove from pending on error
        this.pendingRequests.delete(key);
        throw error;
      });

    // Store as pending
    this.pendingRequests.set(key, { promise, timestamp: Date.now() });

    return promise;
  }

  /**
   * Clear cache for a specific key or pattern
   */
  clearCache(keyOrPattern?: string) {
    if (!keyOrPattern) {
      this.cache.clear();
      this.pendingRequests.clear();
      return;
    }

    // Clear specific key
    if (!keyOrPattern.includes('*')) {
      this.cache.delete(keyOrPattern);
      this.pendingRequests.delete(keyOrPattern);
      return;
    }

    // Clear by pattern (simple wildcard support)
    const pattern = keyOrPattern.replace('*', '');
    for (const key of this.cache.keys()) {
      if (key.startsWith(pattern)) {
        this.cache.delete(key);
      }
    }
    for (const key of this.pendingRequests.keys()) {
      if (key.startsWith(pattern)) {
        this.pendingRequests.delete(key);
      }
    }
  }

  /**
   * Get cache statistics for debugging
   */
  getStats() {
    return {
      cacheSize: this.cache.size,
      pendingRequests: this.pendingRequests.size,
      cacheKeys: Array.from(this.cache.keys()),
      pendingKeys: Array.from(this.pendingRequests.keys())
    };
  }
}

// Global instance
export const requestDeduplicator = new RequestDeduplicator();

/**
 * Helper function to create cache keys for common patterns
 */
export const createCacheKey = {
  userRoles: (userId: string, filters?: Record<string, any>) => 
    `user_roles:${userId}${filters ? ':' + JSON.stringify(filters) : ''}`,
  
  organizations: (userId: string) => 
    `organizations:${userId}`,
  
  locations: (userId: string, organizationId: string) => 
    `locations:${userId}:${organizationId}`,
  
  systemAdmin: (userId: string) => 
    `system_admin:${userId}`,
  
  patients: (organizationId: string, filters?: Record<string, any>) => 
    `patients:${organizationId}${filters ? ':' + JSON.stringify(filters) : ''}`,
  
  appointments: (organizationId: string, filters?: Record<string, any>) => 
    `appointments:${organizationId}${filters ? ':' + JSON.stringify(filters) : ''}`,
};
