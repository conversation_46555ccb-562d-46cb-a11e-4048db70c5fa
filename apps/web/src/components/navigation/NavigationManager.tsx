import { LoadingScreen } from "@/components/ui/loading-screen";
import { useAuth } from "@/hooks/useAuth";
import { useUserRoles } from "@/hooks/useUserRoles";
import { useOrganizationStore } from "@/stores/organization-store";
import { useEffect, useMemo, useRef, useState } from "react";
import { Navigate, useLocation } from "react-router-dom";

// Define route types for cleaner navigation logic
type RouteConfig = {
  public: string[];
  protected: string[];
  protectedPatterns: string[]; // Dynamic routes that start with these patterns
  special: {
    root: string;
    defaultAuthenticated: string;
    defaultUnauthenticated: string;
  };
};

type NavigationManagerProps = {
  children: React.ReactNode;
};

export function NavigationManager({ children }: NavigationManagerProps) {
  const { user, organization, isLoading } = useAuth();
  const { isSystemAdmin, isLoading: rolesLoading } = useUserRoles();
  const { isLoading: orgStoreLoading } = useOrganizationStore();
  const location = useLocation();

  // Track redirect attempts to prevent infinite loops
  const redirectCountRef = useRef(0);
  const lastRedirectTimeRef = useRef(0);
  const lastPathRef = useRef("");

  // Track loading to prevent infinite loading screens
  const [loadingStartTime] = useState(() => Date.now());
  const [shouldBypassLoading, setShouldBypassLoading] = useState(false);

  // Track render safety to prevent blank screens
  const [safetyRenderEnabled, setSafetyRenderEnabled] = useState(false);

  // Define routes configuration
  const routes: RouteConfig = useMemo(
    () => ({
      public: ["/login", "/register", "/reset-password", "/setup"],
      protected: [
        "/dashboard",
        "/settings",
        "/profile",
        "/organizations",
        "/patients",
      ],
      protectedPatterns: ["/patients/", "/organizations/"], // Dynamic routes that start with these patterns
      special: {
        root: "/",
        defaultAuthenticated: "/dashboard",
        defaultUnauthenticated: "/login",
      },
    }),
    [],
  );

  // Safety timeout to prevent infinite loading
  useEffect(() => {
    const safetyTimeout = setTimeout(() => {
      setSafetyRenderEnabled(true);
    }, 10000); // 10 seconds safety timeout

    return () => clearTimeout(safetyTimeout);
  }, []);

  // Loading bypass after timeout
  useEffect(() => {
    const loadingTimeout = setTimeout(() => {
      if (isLoading && Date.now() - loadingStartTime > 5000) {
        setShouldBypassLoading(true);
      }
    }, 5000); // 5 seconds timeout

    return () => clearTimeout(loadingTimeout);
  }, [isLoading, loadingStartTime]);

  // Handle navigation logic
  const shouldRedirect = useMemo(() => {
    // Skip during initial loading unless safety timeout reached
    // Include roles loading and org store loading to prevent premature redirects
    if ((isLoading || rolesLoading || orgStoreLoading) && !shouldBypassLoading && !safetyRenderEnabled) {
      return null;
    }

    const currentPath = location.pathname;

    // Don't redirect if we're already on the target path
    if (currentPath === lastPathRef.current) {
      return null;
    }

    // Update last path
    lastPathRef.current = currentPath;

    // Handle root path
    if (currentPath === routes.special.root) {
      return user
        ? routes.special.defaultAuthenticated
        : routes.special.defaultUnauthenticated;
    }

    // Public routes are always accessible
    if (routes.public.includes(currentPath)) {
      // But redirect authenticated users away from auth pages
      if (
        user &&
        ["/login", "/register", "/reset-password"].includes(currentPath)
      ) {
        return routes.special.defaultAuthenticated;
      }
      return null;
    }

    // Protected routes require authentication
    const isProtectedRoute =
      routes.protected.includes(currentPath) ||
      routes.protectedPatterns.some((pattern) =>
        currentPath.startsWith(pattern),
      );

    if (isProtectedRoute) {
      if (!user) {
        return routes.special.defaultUnauthenticated;
      }
      // Most protected routes also require an organization, except /organizations itself
      if (
        !organization &&
        currentPath !== "/setup" &&
        currentPath !== "/organizations"
      ) {
        // Check if we're still loading organization data to avoid premature redirects
        if (isLoading || rolesLoading || orgStoreLoading) {
          return null; // Don't redirect while still loading
        }

        // System admins should go to organization selection, not setup
        if (isSystemAdmin) {
          return "/organizations";
        }
        return "/setup";
      }
      return null;
    }

    // Default to dashboard for authenticated users, login for others
    return user
      ? routes.special.defaultAuthenticated
      : routes.special.defaultUnauthenticated;
  }, [
    user,
    organization,
    location.pathname,
    isLoading,
    rolesLoading,
    shouldBypassLoading,
    safetyRenderEnabled,
    routes,
    isSystemAdmin,
  ]);

  // Handle the actual redirect
  if (shouldRedirect) {
    // Prevent infinite redirects
    const now = Date.now();
    if (now - lastRedirectTimeRef.current < 1000) {
      redirectCountRef.current++;
      if (redirectCountRef.current > 5) {
        console.error("Too many redirects, rendering current page");
        return <>{children}</>;
      }
    } else {
      redirectCountRef.current = 0;
    }
    lastRedirectTimeRef.current = now;

    return <Navigate to={shouldRedirect} replace />;
  }

  // Show loading screen during initial load
  if (isLoading && !shouldBypassLoading && !safetyRenderEnabled) {
    return <LoadingScreen />;
  }

  // Render children if no redirect needed
  return <>{children}</>;
}
