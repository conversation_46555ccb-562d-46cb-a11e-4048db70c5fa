import { DashboardSidebar } from "@/components/dashboard/DashboardSidebar";
import { UserProfileHeader } from "@/components/dashboard/UserProfileHeader";
import { AuthDebugger } from "@/components/ui/auth-debugger";
import { Toaster } from "@/components/ui/toaster";
import { useAuth } from "@/hooks/useAuth";
import { useState } from "react";
import { Outlet } from "react-router-dom";

export function DashboardLayout() {
  const { authState } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(true);

  // If we're still loading authentication, show loading state
  if (authState?.status === "initializing") {
    return (
      <div className="flex h-full w-full bg-background overflow-hidden items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
        <Toaster />
        <AuthDebugger />
      </div>
    );
  }

  return (
    <div className="flex h-full w-full bg-background overflow-hidden">
      {/* Sidebar */}
      <DashboardSidebar open={sidebarOpen} setOpen={setSidebarOpen} />

      {/* Main content */}
      <div className="flex flex-col flex-1 w-full overflow-hidden">
        {/* Top navigation */}
        <UserProfileHeader toggleSidebar={() => setSidebarOpen(!sidebarOpen)} />

        {/* Main content area */}
        <main className="flex-1 overflow-auto p-6 custom-scrollbar">
          <div className="container mx-auto max-w-7xl">
            <Outlet />
          </div>
        </main>
      </div>

      {/* Global UI elements */}
      <Toaster />
      <AuthDebugger />
    </div>
  );
}
